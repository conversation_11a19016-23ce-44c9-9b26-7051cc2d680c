/*
 * SPDX-FileCopyrightText: 2023 Inria
 * SPDX-FileCopyrightText: 2023 <PERSON><PERSON>
 *
 * SPDX-License-Identifier: Apache-2.0 OR LGPL-2.1-or-later
 */

//! Indexed dictionaries.

pub mod elias_fano;
pub use elias_fano::{<PERSON><PERSON><PERSON>, EliasFanoBuilder, EliasFanoConcurrentBuilder};

pub mod rear_coded_list;
pub use rear_coded_list::{RearCodedList, RearCodedListBuilder};

pub mod slice_seq;
pub use slice_seq::SliceSeq;

pub mod vfilter;
pub use vfilter::VFilter;
