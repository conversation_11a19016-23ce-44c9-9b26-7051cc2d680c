[package]
name = "sux"
authors = [
	"<PERSON><PERSON><PERSON> <<EMAIL>>",
	"<PERSON><PERSON> <<EMAIL>>",
]
description = "A pure Rust implementation of succinct and compressed data structures"
version = "0.8.0"
edition = "2021"
repository = "https://github.com/vigna/sux-rs/"
license = "Apache-2.0 OR LGPL-2.1-or-later"
readme = "README.md"
categories = ["compression", "data-structures"]
keywords = ["succinct", "rank", "select"]

[dependencies]
anyhow = "1.0.79"
bitflags = "2.4.2"
common_traits = "0.12.0"
libc = "0.2.147"
log = "0.4.20"
dsi-progress-logger = "0.8.1"
tempfile = "3.9.0"
lender = "0.3.1"
#epserde = "0.8.0"
epserde = { git = "https://github.com/vigna/epserde.git" }
zstd = { version = "0.13.1" }
flate2 = "1.0.28"
rand = { version = "0.9.0", features = ["small_rng"] }
rdst = "0.20.12"
mem_dbg = "0.3.0"
rayon = { version = "1.8.1", optional = true }
env_logger = { version = "0.11.0" }
ambassador = "0.4.0"
impl-tools = "0.11.2"
clap = { version = "4.4.18", features = ["derive"] }
crossbeam-channel = "0.5.14"
arbitrary-chunks = "0.4.1"
derivative = "2.2.0"
derive_setters = "0.1.6"
thiserror = "2.0.11"
itertools = "0.14.0"
lambert_w = { version = "1.0.17", default-features = false, features = ["std"] }

# Fuzz
arbitrary = { version = "1.3.2", features = ["derive"], optional = true }
xxhash-rust = { version = "0.8.15", features = ["xxh3"] }
thread-priority = "2.1.0"
value-traits = "0.1.4"

[dev-dependencies]
maligned = "0.2.1"
criterion = { version = "0.6.0", features = ["html_reports"] }

[features]
default = ["rayon", "mmap"]
cli = [
] # Build the binary utils (it's here so that cargo bench doesn't build them)
fuzz = ["dep:arbitrary"]
slow_tests = [] # Run slow tests (use --release)
mwhc = [] # Compile MWHC data structures (mainly for benchmarking)
no_logging = [
] # Disable logging for binaries that build functions and filters (for benchmarking)
rayon = ["dep:rayon"]
mmap = ["epserde/mmap"]

[profile.release]
opt-level = 3 # like --release
#lto = "fat"              # Full LTO
debug = true             # Include debug info.
overflow-checks = false  # Disable integer overflow checks.
debug-assertions = false # Enables debug assertions.
#codegen-units = 1        # slower compile times, but maybe better perf

[profile.bench]
opt-level = 3            # like --release
lto = "fat"              # Full LTO
overflow-checks = false  # Disable integer overflow checks.
debug = false            # Include debug info.
debug-assertions = false # Enables debug assertions.
codegen-units = 1        # slower compile times, but maybe better perf

[lib]
bench = false

[[bin]]
name = "rcl"
required-features = ["cli"]

[[bin]]
name = "vfunc"
required-features = ["cli", "rayon"]

[[bin]]
name = "vfilter"
required-features = ["cli", "rayon"]

[[bin]]
name = "mem_usage"
required-features = ["cli"]

[[example]]
name = "bench_bit_field_vec"
required-features = ["cli"]

[[example]]
name = "bench_rank9"
required-features = ["cli"]

[[example]]
name = "bench_elias_fano"
required-features = ["cli"]

[[example]]
name = "bench_rear_coded_list"
required-features = ["cli"]

[[bench]]
name = "sux"
harness = false
