[package]
name = "sux-fuzz"
version = "0.0.0"
publish = false
edition = "2021"

[package.metadata]
cargo-fuzz = true

[dependencies]
libfuzzer-sys = "0.4"

[dependencies.sux]
path = ".."
#default-features = false
features = ["fuzz"]

# Prevent this from interfering with workspaces
[workspace]
members = ["."]

[profile.release]
debug = 1

[[bin]]
name = "select"
path = "fuzz_targets/select.rs"
test = false
doc = false
